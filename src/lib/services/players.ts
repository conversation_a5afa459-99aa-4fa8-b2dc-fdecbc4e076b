import { supabase } from '../supabase/client';
import type { Database } from '../supabase/types';
import { logger } from '@/lib/utils/logger';

type Player = Database['public']['Tables']['players']['Row'];
type InsertPlayer = Database['public']['Tables']['players']['Insert'];
type UpdatePlayer = Database['public']['Tables']['players']['Update'];

export const playersService = {
  async getAll() {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data;
  },

  async getById(id: string) {
    const { data, error } = await supabase
      .from('players')
      .select(`
        *,
        tournament_results(
          *,
          tournament:tournaments(
            *,
            store:stores(*)
          )
        )
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async getByAuthUserId(authUserId: string): Promise<Player> {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .eq('auth_user_id', authUserId)
      .single();

    if (error) throw error;
    return data;
  },

  async getCurrent(): Promise<Player> {
    logger.debug('Getting current player via RPC', { component: 'playersService' });

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .rpc('ensure_player_for_current_user');

    if (error) {
      logger.error('Error getting current player', error, { component: 'playersService' });
      throw error;
    }

    logger.debug('Current player retrieved', {
      component: 'playersService',
      playerId: data.id,
      onboardingCompleted: data.onboarding_completed
    });

    return data;
  },

  async completeOnboarding(firstName: string, lastName: string): Promise<Player> {
    logger.debug('Completing onboarding', { 
      component: 'playersService', 
      firstName: firstName?.length, 
      lastName: lastName?.length 
    });
    
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .rpc('complete_onboarding', {
        p_first: firstName.trim(),
        p_last: lastName.trim()
      });
    
    if (error) {
      logger.error('Error completing onboarding', error, { component: 'playersService' });
      throw error;
    }
    
    logger.debug('Onboarding completed successfully', { 
      component: 'playersService', 
      playerId: data.id, 
      fullName: data.name 
    });
    
    return data;
  },

  async getRanking() {
    logger.debug('Fetching ranking data (public-safe)…', { component: 'playersService' });

    // 1) Load all tournament results (player_id, points) and aggregate client-side
    const { data: results, error: resErr } = await supabase
      .from('tournament_results')
      .select('player_id, points');

    if (resErr) {
      logger.error('Error fetching tournament_results for ranking', resErr, { component: 'playersService' });
      throw resErr;
    }

    // Aggregate totals and counts by player_id
    const totals = new Map<string, { totalPoints: number; tournamentsPlayed: number }>();
    for (const r of results || []) {
      const pid = (r as { player_id: string; points: number }).player_id;
      const pts = (r as { player_id: string; points: number }).points || 0;
      const prev = totals.get(pid) || { totalPoints: 0, tournamentsPlayed: 0 };
      prev.totalPoints += pts;
      prev.tournamentsPlayed += 1;
      totals.set(pid, prev);
    }

    // 2) Fetch public player names (only safe fields for ranking)
    type PublicPlayer = { id: string; name: string };
    const { data: publicPlayers, error: ppErr } = await supabase
      .from('players')
      .select('id, name');

    if (ppErr) {
      logger.error('Error fetching players for ranking', ppErr, { component: 'playersService' });
      throw ppErr;
    }

    const byId = new Map<string, PublicPlayer>();
    for (const p of (publicPlayers as PublicPlayer[]) || []) byId.set(p.id, p);

    // 3) Build final list: include only players who have results (to match previous behavior)
    const playersWithPoints = Array.from(totals.entries()).map(([player_id, agg]) => {
      const p = byId.get(player_id);
      const name = p?.name || 'Giocatore';
      return {
        // Minimal player row shape to satisfy UI; unused fields are omitted
        id: player_id,
        name,
        // Attach computed props used by UI
        totalPoints: agg.totalPoints,
        tournamentsPlayed: agg.tournamentsPlayed,
      } as unknown as import('@/types/ranking').Player;
    });

    // Sort by name to preserve previous ordering before client sorting
    playersWithPoints.sort((a, b) => a.name.localeCompare(b.name));

    logger.debug('Processed players with points (public-safe)', { component: 'playersService', count: playersWithPoints.length });

    return playersWithPoints;
  },

  async create(player: InsertPlayer) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('players')
      .insert(player)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, player: UpdatePlayer) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await (supabase as any)
      .from('players')
      .update(player)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string) {
    const { error } = await supabase
      .from('players')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
}; 